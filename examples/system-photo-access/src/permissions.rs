//! 权限管理模块
//!
//! 处理系统相册访问权限的检查、请求和管理。

use crate::error::{PhotoError, PhotoResult};
use serde::{Deserialize, Serialize};

/// 相册访问权限状态
#[derive(Debug, Clone, PartialEq, Eq, Serialize, Deserialize)]
pub enum PhotoPermissionStatus {
    /// 尚未请求权限
    NotDetermined,
    /// 权限受限（家长控制等）
    Restricted,
    /// 用户拒绝权限
    Denied,
    /// 用户授权完全访问
    Authorized,
    /// 用户授权有限访问（iOS 14+）
    Limited,
}

impl PhotoPermissionStatus {
    /// 检查是否有访问权限
    pub fn has_access(&self) -> bool {
        matches!(self, Self::Authorized | Self::Limited)
    }

    /// 检查是否可以请求权限
    pub fn can_request(&self) -> bool {
        matches!(self, Self::NotDetermined)
    }

    /// 检查是否需要用户手动设置
    pub fn needs_manual_setup(&self) -> bool {
        matches!(self, Self::Denied | Self::Restricted)
    }

    /// 获取状态描述
    pub fn description(&self) -> &'static str {
        match self {
            Self::NotDetermined => "尚未请求相册访问权限",
            Self::Restricted => "相册访问权限受限",
            Self::Denied => "相册访问权限被拒绝",
            Self::Authorized => "已授权完全访问相册",
            Self::Limited => "已授权有限访问相册",
        }
    }

    /// 从整数值创建权限状态
    pub fn from_i32(value: i32) -> Self {
        match value {
            0 => Self::NotDetermined,
            1 => Self::Restricted,
            2 => Self::Denied,
            3 => Self::Authorized,
            4 => Self::Limited,
            _ => Self::Denied, // 安全默认值
        }
    }

    /// 转换为整数值
    pub fn to_i32(&self) -> i32 {
        match self {
            Self::NotDetermined => 0,
            Self::Restricted => 1,
            Self::Denied => 2,
            Self::Authorized => 3,
            Self::Limited => 4,
        }
    }
}

/// 权限请求结果
pub type PermissionResult = PhotoResult<PhotoPermissionStatus>;

// 外部 Swift 函数声明
extern "C" {
    fn photo_bridge_check_permission_status() -> i32;
    fn photo_bridge_request_permission(callback: extern "C" fn(i32));
    fn photo_bridge_open_settings() -> bool;
}

/// 检查当前相册访问权限状态
///
/// # 返回值
/// - `Ok(PhotoPermissionStatus)`: 当前权限状态
/// - `Err(PhotoError)`: 检查失败的错误信息
///
/// # 示例
/// ```rust
/// use system_photo_access::check_permission_status;
///
/// let status = check_permission_status()?;
/// println!("当前权限状态: {:?}", status);
/// ```
pub fn check_permission_status() -> PermissionResult {
    log::debug!("检查相册访问权限状态");

    // 调用 Swift 端的权限检查函数
    let status_code = unsafe { photo_bridge_check_permission_status() };
    let status = PhotoPermissionStatus::from_i32(status_code);

    log::debug!("权限状态检查完成: {:?}", status);
    Ok(status)
}

/// 请求相册访问权限（异步）
///
/// # 返回值
/// - `Ok(PhotoPermissionStatus)`: 用户授权后的权限状态
/// - `Err(PhotoError)`: 权限请求失败的错误信息
///
/// # 注意
/// - 如果权限已被拒绝，此函数不会再次显示权限对话框
/// - 建议在调用前先检查权限状态
///
/// # 示例
/// ```rust
/// use system_photo_access::{request_permission, PhotoPermissionStatus};
///
/// #[tokio::main]
/// async fn main() -> Result<(), Box<dyn std::error::Error>> {
///     let status = request_permission().await?;
///
///     match status {
///         PhotoPermissionStatus::Authorized => {
///             println!("获得完全访问权限");
///         }
///         PhotoPermissionStatus::Limited => {
///             println!("获得有限访问权限");
///         }
///         _ => {
///             println!("权限请求失败");
///         }
///     }
///
///     Ok(())
/// }
/// ```
pub async fn request_permission() -> PermissionResult {
    log::info!("请求相册访问权限");

    // 首先检查当前状态
    let current_status = check_permission_status()?;

    match current_status {
        PhotoPermissionStatus::Authorized | PhotoPermissionStatus::Limited => {
            // 已有权限，直接返回
            log::debug!("权限已存在: {:?}", current_status);
            return Ok(current_status);
        }
        PhotoPermissionStatus::Denied | PhotoPermissionStatus::Restricted => {
            // 权限被拒绝或受限，无法重新请求
            log::warn!("权限被拒绝或受限: {:?}", current_status);
            return Err(match current_status {
                PhotoPermissionStatus::Denied => PhotoError::permission_denied(),
                PhotoPermissionStatus::Restricted => PhotoError::permission_restricted(),
                _ => unreachable!(),
            });
        }
        PhotoPermissionStatus::NotDetermined => {
            // 可以请求权限
            log::debug!("开始请求权限");
        }
    }

    // TODO: 实现异步权限请求
    // 这里需要与 Swift 端的异步权限请求函数集成
    // let result = ffi::request_permission().await;

    // 临时实现：模拟权限请求
    tokio::time::sleep(tokio::time::Duration::from_millis(100)).await;

    // 模拟用户授权
    let new_status = PhotoPermissionStatus::Authorized;
    log::info!("权限请求完成: {:?}", new_status);

    Ok(new_status)
}

/// 请求相册访问权限（回调版本）
///
/// # 参数
/// - `callback`: 权限请求完成后的回调函数
///
/// # 示例
/// ```rust
/// use system_photo_access::request_permission_with_callback;
///
/// request_permission_with_callback(|result| {
///     match result {
///         Ok(status) => println!("权限状态: {:?}", status),
///         Err(error) => println!("权限请求失败: {}", error),
///     }
/// });
/// ```
pub fn request_permission_with_callback<F>(callback: F)
where
    F: FnOnce(PermissionResult) + Send + 'static,
{
    log::debug!("使用回调方式请求权限");

    // 在异步运行时中执行权限请求
    tokio::spawn(async move {
        let result = request_permission().await;
        callback(result);
    });
}

/// 打开系统设置页面以便用户手动授权
///
/// # 返回值
/// - `Ok(())`: 成功打开设置页面
/// - `Err(PhotoError)`: 打开失败的错误信息
///
/// # 示例
/// ```rust
/// use system_photo_access::open_settings;
///
/// if let Err(error) = open_settings() {
///     println!("无法打开设置页面: {}", error);
/// }
/// ```
pub fn open_settings() -> PhotoResult<()> {
    log::info!("尝试打开系统设置页面");

    // 调用 Swift 端的设置页面打开函数
    let success = unsafe { photo_bridge_open_settings() };

    if success {
        log::info!("成功打开设置页面");
        Ok(())
    } else {
        let error = PhotoError::system_error("无法打开设置页面");
        log::error!("打开设置页面失败: {}", error);
        Err(error)
    }
}

/// 确保拥有相册访问权限
///
/// 这是一个便利函数，会自动检查权限状态并在需要时请求权限。
///
/// # 返回值
/// - `Ok(PhotoPermissionStatus)`: 最终的权限状态
/// - `Err(PhotoError)`: 权限获取失败的错误信息
///
/// # 示例
/// ```rust
/// use system_photo_access::ensure_photo_permission;
///
/// #[tokio::main]
/// async fn main() -> Result<(), Box<dyn std::error::Error>> {
///     match ensure_photo_permission().await {
///         Ok(status) if status.has_access() => {
///             println!("成功获得相册访问权限");
///             // 继续访问相册
///         }
///         Ok(_) => {
///             println!("权限获取失败");
///         }
///         Err(error) => {
///             println!("权限检查失败: {}", error);
///         }
///     }
///
///     Ok(())
/// }
/// ```
pub async fn ensure_photo_permission() -> PermissionResult {
    log::debug!("确保拥有相册访问权限");

    // 检查当前权限状态
    let current_status = check_permission_status()?;

    match current_status {
        PhotoPermissionStatus::Authorized | PhotoPermissionStatus::Limited => {
            // 已有权限，直接返回
            log::debug!("已有相册访问权限: {:?}", current_status);
            Ok(current_status)
        }
        PhotoPermissionStatus::NotDetermined => {
            // 尚未请求，发起权限请求
            log::debug!("尚未请求权限，开始请求");
            request_permission().await
        }
        PhotoPermissionStatus::Denied => {
            // 被拒绝，返回错误
            log::warn!("相册访问权限被拒绝");
            Err(PhotoError::permission_denied())
        }
        PhotoPermissionStatus::Restricted => {
            // 受限制，返回错误
            log::warn!("相册访问权限受限");
            Err(PhotoError::permission_restricted())
        }
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_permission_status_methods() {
        assert!(PhotoPermissionStatus::Authorized.has_access());
        assert!(PhotoPermissionStatus::Limited.has_access());
        assert!(!PhotoPermissionStatus::Denied.has_access());

        assert!(PhotoPermissionStatus::NotDetermined.can_request());
        assert!(!PhotoPermissionStatus::Denied.can_request());

        assert!(PhotoPermissionStatus::Denied.needs_manual_setup());
        assert!(PhotoPermissionStatus::Restricted.needs_manual_setup());
        assert!(!PhotoPermissionStatus::Authorized.needs_manual_setup());
    }

    #[test]
    fn test_permission_status_description() {
        let status = PhotoPermissionStatus::Authorized;
        assert!(!status.description().is_empty());
    }

    #[test]
    fn test_check_permission_status() {
        let result = check_permission_status();
        assert!(result.is_ok());
    }

    #[tokio::test]
    async fn test_request_permission() {
        let result = request_permission().await;
        assert!(result.is_ok());
    }

    #[tokio::test]
    async fn test_ensure_photo_permission() {
        let result = ensure_photo_permission().await;
        assert!(result.is_ok());
    }

    #[test]
    fn test_open_settings() {
        let result = open_settings();
        assert!(result.is_ok());
    }
}
