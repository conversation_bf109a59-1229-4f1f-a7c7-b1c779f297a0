//! 模拟 Swift 函数实现
//! 
//! 这个模块提供了 Swift 桥接函数的模拟实现，
//! 用于在没有真正 Swift 库的情况下进行测试。

/// 模拟的权限检查函数
#[no_mangle]
pub extern "C" fn photo_bridge_check_permission_status() -> i32 {
    // 模拟返回 "未确定" 状态
    0
}

/// 模拟的权限请求函数
#[no_mangle]
pub extern "C" fn photo_bridge_request_permission(_callback: extern "C" fn(i32)) {
    // 模拟权限请求，直接调用回调返回 "已授权" 状态
    // callback(3);
}

/// 模拟的设置页面打开函数
#[no_mangle]
pub extern "C" fn photo_bridge_open_settings() -> bool {
    // 模拟成功打开设置页面
    true
}

/// 模拟的资源数量获取函数
#[no_mangle]
pub extern "C" fn photo_bridge_get_asset_count(_media_type: i32) -> i32 {
    // 模拟返回固定数量的资源
    42
}

/// 模拟的缩略图数据获取函数
#[no_mangle]
pub extern "C" fn photo_bridge_get_thumbnail_data(
    _asset_id: *const std::os::raw::c_char,
    _width: u32,
    _height: u32,
    callback: extern "C" fn(*const u8, i32),
) {
    // 模拟返回空数据
    callback(std::ptr::null(), 0);
}
