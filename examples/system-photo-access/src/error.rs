//! 错误处理模块
//! 
//! 定义了系统相册访问过程中可能出现的各种错误类型。

use thiserror::Error;

/// 相册访问错误类型
#[derive(Debug, Error, Clone, PartialEq, Eq)]
pub enum PhotoError {
    /// 权限被拒绝
    #[error("相册访问权限被拒绝")]
    PermissionDenied,
    
    /// 权限受限
    #[error("相册访问权限受限")]
    PermissionRestricted,
    
    /// 资源不存在
    #[error("媒体资源不存在: {id}")]
    AssetNotFound { id: String },
    
    /// 网络错误
    #[error("网络访问失败: {message}")]
    NetworkError { message: String },
    
    /// 系统错误
    #[error("系统错误: {message}")]
    SystemError { message: String },
    
    /// 数据格式错误
    #[error("数据格式不支持: {format}")]
    UnsupportedFormat { format: String },
    
    /// 内存不足
    #[error("内存不足")]
    OutOfMemory,
    
    /// 操作被取消
    #[error("操作被用户取消")]
    Cancelled,
    
    /// 超时错误
    #[error("操作超时")]
    Timeout,
}

impl PhotoError {
    /// 创建系统错误
    pub fn system_error(message: impl Into<String>) -> Self {
        Self::SystemError {
            message: message.into(),
        }
    }
    
    /// 创建网络错误
    pub fn network_error(message: impl Into<String>) -> Self {
        Self::NetworkError {
            message: message.into(),
        }
    }
    
    /// 创建资源不存在错误
    pub fn asset_not_found(id: impl Into<String>) -> Self {
        Self::AssetNotFound {
            id: id.into(),
        }
    }
    
    /// 创建不支持格式错误
    pub fn unsupported_format(format: impl Into<String>) -> Self {
        Self::UnsupportedFormat {
            format: format.into(),
        }
    }
    
    /// 检查是否为权限相关错误
    pub fn is_permission_error(&self) -> bool {
        matches!(self, Self::PermissionDenied | Self::PermissionRestricted)
    }
    
    /// 检查是否为可重试错误
    pub fn is_retryable(&self) -> bool {
        matches!(
            self,
            Self::NetworkError { .. } | Self::Timeout | Self::OutOfMemory
        )
    }
    
    /// 获取错误代码
    pub fn error_code(&self) -> i32 {
        match self {
            Self::PermissionDenied => 1001,
            Self::PermissionRestricted => 1002,
            Self::AssetNotFound { .. } => 2001,
            Self::NetworkError { .. } => 3001,
            Self::SystemError { .. } => 4001,
            Self::UnsupportedFormat { .. } => 5001,
            Self::OutOfMemory => 6001,
            Self::Cancelled => 7001,
            Self::Timeout => 8001,
        }
    }
    
    /// 获取用户友好的错误描述
    pub fn user_description(&self) -> &'static str {
        match self {
            Self::PermissionDenied => "需要相册访问权限才能继续操作",
            Self::PermissionRestricted => "相册访问权限受限，请检查设备设置",
            Self::AssetNotFound { .. } => "找不到指定的照片或视频",
            Self::NetworkError { .. } => "网络连接出现问题，请检查网络设置",
            Self::SystemError { .. } => "系统出现错误，请稍后重试",
            Self::UnsupportedFormat { .. } => "不支持的文件格式",
            Self::OutOfMemory => "内存不足，请关闭其他应用后重试",
            Self::Cancelled => "操作已取消",
            Self::Timeout => "操作超时，请重试",
        }
    }
    
    /// 获取建议的解决方案
    pub fn suggested_action(&self) -> Option<&'static str> {
        match self {
            Self::PermissionDenied => Some("请到设置中授权相册访问权限"),
            Self::PermissionRestricted => Some("请检查家长控制或企业设备管理设置"),
            Self::NetworkError { .. } => Some("请检查网络连接后重试"),
            Self::OutOfMemory => Some("请关闭其他应用释放内存"),
            Self::Timeout => Some("请检查网络连接或稍后重试"),
            _ => None,
        }
    }
}

/// 错误结果类型别名
pub type PhotoResult<T> = Result<T, PhotoError>;

/// 从 Swift 错误代码转换为 PhotoError
impl From<i32> for PhotoError {
    fn from(code: i32) -> Self {
        match code {
            1001 => Self::PermissionDenied,
            1002 => Self::PermissionRestricted,
            2001 => Self::AssetNotFound { id: "unknown".to_string() },
            3001 => Self::NetworkError { message: "网络错误".to_string() },
            4001 => Self::SystemError { message: "系统错误".to_string() },
            5001 => Self::UnsupportedFormat { format: "unknown".to_string() },
            6001 => Self::OutOfMemory,
            7001 => Self::Cancelled,
            8001 => Self::Timeout,
            _ => Self::SystemError { message: format!("未知错误代码: {}", code) },
        }
    }
}

/// 转换为 Swift 错误代码
impl From<PhotoError> for i32 {
    fn from(error: PhotoError) -> Self {
        error.error_code()
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_error_creation() {
        let error = PhotoError::system_error("测试错误");
        assert!(matches!(error, PhotoError::SystemError { .. }));
        assert_eq!(error.error_code(), 4001);
    }

    #[test]
    fn test_permission_error_check() {
        assert!(PhotoError::PermissionDenied.is_permission_error());
        assert!(PhotoError::PermissionRestricted.is_permission_error());
        assert!(!PhotoError::Timeout.is_permission_error());
    }

    #[test]
    fn test_retryable_error_check() {
        assert!(PhotoError::Timeout.is_retryable());
        assert!(PhotoError::OutOfMemory.is_retryable());
        assert!(!PhotoError::PermissionDenied.is_retryable());
    }

    #[test]
    fn test_error_code_conversion() {
        let error = PhotoError::PermissionDenied;
        let code: i32 = error.clone().into();
        let converted_error = PhotoError::from(code);
        assert_eq!(error, converted_error);
    }

    #[test]
    fn test_user_descriptions() {
        let error = PhotoError::PermissionDenied;
        assert!(!error.user_description().is_empty());
        
        let action = error.suggested_action();
        assert!(action.is_some());
        assert!(!action.unwrap().is_empty());
    }
}
