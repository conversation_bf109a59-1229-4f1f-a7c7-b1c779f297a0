//! # 系统相册访问库
//!
//! 这个库提供了通过 swift-bridge 访问 iOS/macOS 系统相册的功能。
//!
//! ## 功能特性
//!
//! - 相册访问权限管理
//! - 相册内容读取
//! - 图片数据获取
//! - 错误处理和用户引导
//!
//! ## 使用示例
//!
//! ```rust
//! use system_photo_access::*;
//!
//! #[tokio::main]
//! async fn main() -> Result<(), PhotoError> {
//!     // 检查权限状态
//!     let status = check_permission_status()?;
//!
//!     if status != PhotoPermissionStatus::Authorized {
//!         // 请求权限
//!         let new_status = request_permission().await?;
//!         if new_status != PhotoPermissionStatus::Authorized {
//!             return Err(PhotoError::PermissionDenied);
//!         }
//!     }
//!
//!     // 获取相册列表
//!     let query = PhotoQuery::default();
//!     let assets = get_photo_assets(query).await?;
//!
//!     println!("找到 {} 个媒体资源", assets.len());
//!     Ok(())
//! }
//! ```

// 模块声明
pub mod album;
pub mod error;
pub mod image;
pub mod permissions;
pub mod types;

// 测试时使用模拟的 Swift 函数
#[cfg(test)]
pub mod mock_swift;

// 重新导出主要类型和函数
pub use album::{get_asset_by_id, get_asset_count, get_photo_assets};
pub use error::{ErrorContext, Language, PhotoError, PhotoResult, SwiftErrorInfo};
pub use image::{
    get_image_data, get_multiple_images, get_thumbnail, ContentMode, ImageRequestOptions,
};
pub use permissions::{
    check_permission_status, open_settings, request_permission, PhotoPermissionStatus,
};
pub use types::{ImageData, ImageFormat, ImageSize, MediaType, PhotoAsset, PhotoQuery, SortOrder};

// swift-bridge 桥接模块
#[swift_bridge::bridge]
mod ffi {
    // 权限状态枚举
    enum PhotoPermissionStatus {
        NotDetermined,
        Restricted,
        Denied,
        Authorized,
        Limited,
    }

    // 媒体类型枚举
    enum MediaType {
        Image,
        Video,
        Audio,
        Unknown,
    }

    // 图片格式枚举
    enum ImageFormat {
        JPEG,
        PNG,
        HEIC,
        GIF,
        TIFF,
        Unknown,
    }

    // 错误类型
    enum PhotoError {
        PermissionDenied,
        PermissionRestricted,
        AssetNotFound,
        NetworkError,
        SystemError,
        UnsupportedFormat,
        OutOfMemory,
        Cancelled,
        Timeout,
    }

    // 图片尺寸结构体
    #[swift_bridge(swift_repr = "struct")]
    struct ImageSize {
        width: u32,
        height: u32,
    }

    // 导出 Rust 函数给 Swift 使用
    extern "Rust" {
        // 权限管理
        fn bridge_check_permission_status() -> PhotoPermissionStatus;

        // 相册访问
        fn bridge_get_photo_assets_count() -> usize;

        // 图片数据访问
        fn bridge_get_thumbnail_data(asset_id: &str, width: u32, height: u32) -> Vec<u8>;

        // 错误处理
        fn bridge_parse_swift_error(error_json: &str) -> PhotoError;
        fn bridge_create_error_context(
            operation: &str,
            file_path: &str,
            line_number: u32,
        ) -> String;
    }

    // 导入 Swift 函数供 Rust 使用
    extern "Swift" {
        // 带错误信息的权限请求
        fn photo_bridge_request_permission_with_error_sync(
            status_ptr: *mut i32,
            error_json_ptr: *mut *const i8,
        );

        // 带错误信息的缩略图获取
        fn photo_bridge_get_thumbnail_data_with_error_sync(
            asset_id: &str,
            width: u32,
            height: u32,
            data_ptr: *mut *const u8,
            data_len_ptr: *mut i32,
            error_json_ptr: *mut *const i8,
        );
    }
}

// 桥接函数实现
use ffi::{
    MediaType as BridgeMediaType, PhotoError as BridgePhotoError,
    PhotoPermissionStatus as BridgePermissionStatus,
};

/// 桥接函数：检查权限状态
fn bridge_check_permission_status() -> BridgePermissionStatus {
    match check_permission_status() {
        Ok(status) => convert_permission_status_to_bridge(status),
        Err(_) => BridgePermissionStatus::Denied,
    }
}

/// 桥接函数：获取资源数量
fn bridge_get_photo_assets_count() -> usize {
    // 使用运行时来执行异步函数
    let rt = tokio::runtime::Runtime::new().unwrap();
    match rt.block_on(get_asset_count(None)) {
        Ok(count) => count,
        Err(_) => 0,
    }
}

/// 桥接函数：获取缩略图数据
fn bridge_get_thumbnail_data(asset_id: &str, width: u32, height: u32) -> Vec<u8> {
    log::debug!("请求缩略图: {} ({}x{})", asset_id, width, height);

    // 使用运行时来执行异步函数
    let rt = tokio::runtime::Runtime::new().unwrap();
    let size = ImageSize::new(width, height);

    match rt.block_on(get_thumbnail(asset_id, size)) {
        Ok(image_data) => image_data.data,
        Err(_) => Vec::new(),
    }
}

/// 桥接函数：解析 Swift 错误 JSON
fn bridge_parse_swift_error(error_json: &str) -> BridgePhotoError {
    match serde_json::from_str::<serde_json::Value>(error_json) {
        Ok(json) => {
            let code = json["code"].as_i64().unwrap_or(4001) as i32;
            let message = json["message"].as_str().unwrap_or("未知错误").to_string();
            let domain = json["domain"].as_str().map(|s| s.to_string());

            let context = ErrorContext::new("swift_error_parsing")
                .with_info("error_json", error_json.to_string());

            let photo_error = PhotoError::from_swift_error(message, code, domain, Some(context));
            convert_error_to_bridge(photo_error)
        }
        Err(_) => {
            let error = PhotoError::system_error("无法解析 Swift 错误信息");
            convert_error_to_bridge(error)
        }
    }
}

/// 桥接函数：创建错误上下文
fn bridge_create_error_context(operation: &str, file_path: &str, line_number: u32) -> String {
    let context = ErrorContext::new(operation).with_file(file_path, line_number);

    // 将上下文序列化为 JSON
    match serde_json::to_string(&context) {
        Ok(json) => json,
        Err(_) => format!(
            r#"{{"operation":"{}","file_path":"{}","line_number":{}}}"#,
            operation, file_path, line_number
        ),
    }
}

// 类型转换函数
fn convert_permission_status_to_bridge(status: PhotoPermissionStatus) -> BridgePermissionStatus {
    match status {
        PhotoPermissionStatus::NotDetermined => BridgePermissionStatus::NotDetermined,
        PhotoPermissionStatus::Restricted => BridgePermissionStatus::Restricted,
        PhotoPermissionStatus::Denied => BridgePermissionStatus::Denied,
        PhotoPermissionStatus::Authorized => BridgePermissionStatus::Authorized,
        PhotoPermissionStatus::Limited => BridgePermissionStatus::Limited,
    }
}

fn convert_media_type_from_bridge(media_type: BridgeMediaType) -> MediaType {
    match media_type {
        BridgeMediaType::Image => MediaType::Image,
        BridgeMediaType::Video => MediaType::Video,
        BridgeMediaType::Audio => MediaType::Audio,
        BridgeMediaType::Unknown => MediaType::Unknown,
    }
}

fn convert_error_to_bridge(error: PhotoError) -> BridgePhotoError {
    match error {
        PhotoError::PermissionDenied { .. } => BridgePhotoError::PermissionDenied,
        PhotoError::PermissionRestricted { .. } => BridgePhotoError::PermissionRestricted,
        PhotoError::AssetNotFound { .. } => BridgePhotoError::AssetNotFound,
        PhotoError::NetworkError { .. } => BridgePhotoError::NetworkError,
        PhotoError::SystemError { .. } => BridgePhotoError::SystemError,
        PhotoError::UnsupportedFormat { .. } => BridgePhotoError::UnsupportedFormat,
        PhotoError::OutOfMemory { .. } => BridgePhotoError::OutOfMemory,
        PhotoError::Cancelled { .. } => BridgePhotoError::Cancelled,
        PhotoError::Timeout { .. } => BridgePhotoError::Timeout,
        PhotoError::SwiftBridgeError { .. } => BridgePhotoError::SystemError,
    }
}

// 版本信息
pub const VERSION: &str = env!("CARGO_PKG_VERSION");

/// 初始化库
///
/// 这个函数应该在使用库的其他功能之前调用。
/// 它会初始化日志记录和其他必要的组件。
pub fn init() {
    // 初始化日志记录
    if std::env::var("RUST_LOG").is_err() {
        std::env::set_var("RUST_LOG", "system_photo_access=info");
    }

    // 初始化 tracing
    tracing_subscriber::fmt::init();

    log::info!("系统相册访问库已初始化，版本: {}", VERSION);
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_library_initialization() {
        init();
        assert_eq!(VERSION, env!("CARGO_PKG_VERSION"));
    }

    #[test]
    fn test_image_size_creation() {
        let size = ImageSize {
            width: 100,
            height: 200,
        };
        assert_eq!(size.width, 100);
        assert_eq!(size.height, 200);
    }
}
