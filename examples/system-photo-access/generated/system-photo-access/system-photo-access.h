// File automatically generated by swift-bridge.
#include <stdint.h>
#include <stdbool.h>
typedef enum __swift_bridge__$PhotoPermissionStatusTag { __swift_bridge__$PhotoPermissionStatus$NotDetermined, __swift_bridge__$PhotoPermissionStatus$Restricted, __swift_bridge__$PhotoPermissionStatus$Denied, __swift_bridge__$PhotoPermissionStatus$Authorized, __swift_bridge__$PhotoPermissionStatus$Limited, } __swift_bridge__$PhotoPermissionStatusTag;
typedef struct __swift_bridge__$PhotoPermissionStatus { __swift_bridge__$PhotoPermissionStatusTag tag; } __swift_bridge__$PhotoPermissionStatus;
typedef struct __swift_bridge__$Option$PhotoPermissionStatus { bool is_some; __swift_bridge__$PhotoPermissionStatus val; } __swift_bridge__$Option$PhotoPermissionStatus;

void* __swift_bridge__$Vec_PhotoPermissionStatus$new(void);
void __swift_bridge__$Vec_PhotoPermissionStatus$drop(void* vec_ptr);
void __swift_bridge__$Vec_PhotoPermissionStatus$push(void* vec_ptr, __swift_bridge__$PhotoPermissionStatus item);
__swift_bridge__$Option$PhotoPermissionStatus __swift_bridge__$Vec_PhotoPermissionStatus$pop(void* vec_ptr);
__swift_bridge__$Option$PhotoPermissionStatus __swift_bridge__$Vec_PhotoPermissionStatus$get(void* vec_ptr, uintptr_t index);
__swift_bridge__$Option$PhotoPermissionStatus __swift_bridge__$Vec_PhotoPermissionStatus$get_mut(void* vec_ptr, uintptr_t index);
uintptr_t __swift_bridge__$Vec_PhotoPermissionStatus$len(void* vec_ptr);
void* __swift_bridge__$Vec_PhotoPermissionStatus$as_ptr(void* vec_ptr);

typedef enum __swift_bridge__$MediaTypeTag { __swift_bridge__$MediaType$Image, __swift_bridge__$MediaType$Video, __swift_bridge__$MediaType$Audio, __swift_bridge__$MediaType$Unknown, } __swift_bridge__$MediaTypeTag;
typedef struct __swift_bridge__$MediaType { __swift_bridge__$MediaTypeTag tag; } __swift_bridge__$MediaType;
typedef struct __swift_bridge__$Option$MediaType { bool is_some; __swift_bridge__$MediaType val; } __swift_bridge__$Option$MediaType;

void* __swift_bridge__$Vec_MediaType$new(void);
void __swift_bridge__$Vec_MediaType$drop(void* vec_ptr);
void __swift_bridge__$Vec_MediaType$push(void* vec_ptr, __swift_bridge__$MediaType item);
__swift_bridge__$Option$MediaType __swift_bridge__$Vec_MediaType$pop(void* vec_ptr);
__swift_bridge__$Option$MediaType __swift_bridge__$Vec_MediaType$get(void* vec_ptr, uintptr_t index);
__swift_bridge__$Option$MediaType __swift_bridge__$Vec_MediaType$get_mut(void* vec_ptr, uintptr_t index);
uintptr_t __swift_bridge__$Vec_MediaType$len(void* vec_ptr);
void* __swift_bridge__$Vec_MediaType$as_ptr(void* vec_ptr);

typedef enum __swift_bridge__$ImageFormatTag { __swift_bridge__$ImageFormat$JPEG, __swift_bridge__$ImageFormat$PNG, __swift_bridge__$ImageFormat$HEIC, __swift_bridge__$ImageFormat$GIF, __swift_bridge__$ImageFormat$TIFF, __swift_bridge__$ImageFormat$Unknown, } __swift_bridge__$ImageFormatTag;
typedef struct __swift_bridge__$ImageFormat { __swift_bridge__$ImageFormatTag tag; } __swift_bridge__$ImageFormat;
typedef struct __swift_bridge__$Option$ImageFormat { bool is_some; __swift_bridge__$ImageFormat val; } __swift_bridge__$Option$ImageFormat;

void* __swift_bridge__$Vec_ImageFormat$new(void);
void __swift_bridge__$Vec_ImageFormat$drop(void* vec_ptr);
void __swift_bridge__$Vec_ImageFormat$push(void* vec_ptr, __swift_bridge__$ImageFormat item);
__swift_bridge__$Option$ImageFormat __swift_bridge__$Vec_ImageFormat$pop(void* vec_ptr);
__swift_bridge__$Option$ImageFormat __swift_bridge__$Vec_ImageFormat$get(void* vec_ptr, uintptr_t index);
__swift_bridge__$Option$ImageFormat __swift_bridge__$Vec_ImageFormat$get_mut(void* vec_ptr, uintptr_t index);
uintptr_t __swift_bridge__$Vec_ImageFormat$len(void* vec_ptr);
void* __swift_bridge__$Vec_ImageFormat$as_ptr(void* vec_ptr);

typedef enum __swift_bridge__$PhotoErrorTag { __swift_bridge__$PhotoError$PermissionDenied, __swift_bridge__$PhotoError$PermissionRestricted, __swift_bridge__$PhotoError$AssetNotFound, __swift_bridge__$PhotoError$NetworkError, __swift_bridge__$PhotoError$SystemError, __swift_bridge__$PhotoError$UnsupportedFormat, __swift_bridge__$PhotoError$OutOfMemory, __swift_bridge__$PhotoError$Cancelled, __swift_bridge__$PhotoError$Timeout, } __swift_bridge__$PhotoErrorTag;
typedef struct __swift_bridge__$PhotoError { __swift_bridge__$PhotoErrorTag tag; } __swift_bridge__$PhotoError;
typedef struct __swift_bridge__$Option$PhotoError { bool is_some; __swift_bridge__$PhotoError val; } __swift_bridge__$Option$PhotoError;

void* __swift_bridge__$Vec_PhotoError$new(void);
void __swift_bridge__$Vec_PhotoError$drop(void* vec_ptr);
void __swift_bridge__$Vec_PhotoError$push(void* vec_ptr, __swift_bridge__$PhotoError item);
__swift_bridge__$Option$PhotoError __swift_bridge__$Vec_PhotoError$pop(void* vec_ptr);
__swift_bridge__$Option$PhotoError __swift_bridge__$Vec_PhotoError$get(void* vec_ptr, uintptr_t index);
__swift_bridge__$Option$PhotoError __swift_bridge__$Vec_PhotoError$get_mut(void* vec_ptr, uintptr_t index);
uintptr_t __swift_bridge__$Vec_PhotoError$len(void* vec_ptr);
void* __swift_bridge__$Vec_PhotoError$as_ptr(void* vec_ptr);

typedef struct __swift_bridge__$ImageSize { uint32_t width; uint32_t height; } __swift_bridge__$ImageSize;
typedef struct __swift_bridge__$Option$ImageSize { bool is_some; __swift_bridge__$ImageSize val; } __swift_bridge__$Option$ImageSize;
struct __swift_bridge__$PhotoPermissionStatus __swift_bridge__$bridge_check_permission_status(void);
uintptr_t __swift_bridge__$bridge_get_photo_assets_count(void);
void* __swift_bridge__$bridge_get_thumbnail_data(struct RustStr asset_id, uint32_t width, uint32_t height);


