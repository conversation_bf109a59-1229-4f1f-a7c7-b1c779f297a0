//
//  PhotoBridge.swift
//  SystemPhotoAccess
//
//  Created by swift-bridge on 2025-01-08.
//  Copyright © 2025 swift-bridge contributors. All rights reserved.
//

import Foundation
import Photos
import PhotosUI

/// 系统相册访问桥接模块
/// 
/// 这个模块提供了 Rust 和 Swift 之间的桥接功能，
/// 用于访问 iOS/macOS 系统相册。
public class PhotoBridge {
    
    // MARK: - 单例模式
    
    public static let shared = PhotoBridge()
    
    private init() {
        // 私有初始化，确保单例模式
    }
    
    // MARK: - 权限管理
    
    /// 检查当前相册访问权限状态
    /// 
    /// - Returns: 权限状态的整数表示
    public func checkPermissionStatus() -> Int32 {
        let status = PHPhotoLibrary.authorizationStatus()
        return mapAuthorizationStatusToInt(status)
    }
    
    /// 请求相册访问权限
    /// 
    /// - Parameter completion: 权限请求完成后的回调
    public func requestPermission(completion: @escaping (Int32) -> Void) {
        #if os(iOS)
        if #available(iOS 14, *) {
            PHPhotoLibrary.requestAuthorization(for: .readWrite) { status in
                DispatchQueue.main.async {
                    completion(self.mapAuthorizationStatusToInt(status))
                }
            }
        } else {
            PHPhotoLibrary.requestAuthorization { status in
                DispatchQueue.main.async {
                    completion(self.mapAuthorizationStatusToInt(status))
                }
            }
        }
        #elseif os(macOS)
        PHPhotoLibrary.requestAuthorization { status in
            DispatchQueue.main.async {
                completion(self.mapAuthorizationStatusToInt(status))
            }
        }
        #endif
    }
    
    /// 打开系统设置页面
    /// 
    /// - Returns: 是否成功打开设置页面
    public func openSettings() -> Bool {
        #if os(iOS)
        guard let settingsUrl = URL(string: UIApplication.openSettingsURLString) else {
            return false
        }
        
        if UIApplication.shared.canOpenURL(settingsUrl) {
            UIApplication.shared.open(settingsUrl, completionHandler: nil)
            return true
        }
        #elseif os(macOS)
        // macOS 上打开系统偏好设置的安全与隐私面板
        let workspace = NSWorkspace.shared
        let url = URL(string: "x-apple.systempreferences:com.apple.preference.security?Privacy_Photos")!
        return workspace.open(url)
        #endif
        
        return false
    }
    
    // MARK: - 相册数据访问
    
    /// 获取相册中的媒体资源数量
    /// 
    /// - Parameter mediaType: 媒体类型筛选 (0: 全部, 1: 图片, 2: 视频)
    /// - Returns: 媒体资源数量
    public func getAssetCount(mediaType: Int32) -> Int32 {
        let fetchOptions = PHFetchOptions()
        
        // 根据媒体类型设置筛选条件
        switch mediaType {
        case 1: // 图片
            fetchOptions.predicate = NSPredicate(format: "mediaType = %d", PHAssetMediaType.image.rawValue)
        case 2: // 视频
            fetchOptions.predicate = NSPredicate(format: "mediaType = %d", PHAssetMediaType.video.rawValue)
        default: // 全部
            break
        }
        
        let fetchResult = PHAsset.fetchAssets(with: fetchOptions)
        return Int32(fetchResult.count)
    }
    
    /// 获取缩略图数据
    /// 
    /// - Parameters:
    ///   - assetId: 资源标识符
    ///   - width: 目标宽度
    ///   - height: 目标高度
    ///   - completion: 完成回调，返回图片数据
    public func getThumbnailData(
        assetId: String,
        width: UInt32,
        height: UInt32,
        completion: @escaping (Data?) -> Void
    ) {
        // 根据 assetId 获取 PHAsset
        let fetchOptions = PHFetchOptions()
        fetchOptions.predicate = NSPredicate(format: "localIdentifier = %@", assetId)
        let fetchResult = PHAsset.fetchAssets(with: fetchOptions)
        
        guard let asset = fetchResult.firstObject else {
            completion(nil)
            return
        }
        
        // 配置图片请求选项
        let options = PHImageRequestOptions()
        options.isSynchronous = false
        options.deliveryMode = .highQualityFormat
        options.resizeMode = .exact
        
        let targetSize = CGSize(width: CGFloat(width), height: CGFloat(height))
        
        // 请求图片数据
        PHImageManager.default().requestImage(
            for: asset,
            targetSize: targetSize,
            contentMode: .aspectFit,
            options: options
        ) { image, info in
            guard let image = image else {
                completion(nil)
                return
            }
            
            // 将 UIImage 转换为 Data
            let imageData = image.jpegData(compressionQuality: 0.8)
            completion(imageData)
        }
    }
    
    // MARK: - 私有辅助方法
    
    /// 将 PHAuthorizationStatus 映射为整数
    /// 
    /// - Parameter status: PHAuthorizationStatus
    /// - Returns: 对应的整数值
    private func mapAuthorizationStatusToInt(_ status: PHAuthorizationStatus) -> Int32 {
        switch status {
        case .notDetermined:
            return 0  // NotDetermined
        case .restricted:
            return 1  // Restricted
        case .denied:
            return 2  // Denied
        case .authorized:
            return 3  // Authorized
        case .limited:
            return 4  // Limited (iOS 14+)
        @unknown default:
            return 2  // Denied (安全默认值)
        }
    }
}

// MARK: - C 兼容的桥接函数

/// 初始化相册桥接模块
@_cdecl("photo_bridge_init")
public func photoBridgeInit() -> Bool {
    // 执行必要的初始化操作
    return true
}

/// 检查权限状态的 C 兼容函数
@_cdecl("photo_bridge_check_permission_status")
public func photoBridgeCheckPermissionStatus() -> Int32 {
    return PhotoBridge.shared.checkPermissionStatus()
}

/// 获取资源数量的 C 兼容函数
@_cdecl("photo_bridge_get_asset_count")
public func photoBridgeGetAssetCount(mediaType: Int32) -> Int32 {
    return PhotoBridge.shared.getAssetCount(mediaType: mediaType)
}

/// 打开设置页面的 C 兼容函数
@_cdecl("photo_bridge_open_settings")
public func photoBridgeOpenSettings() -> Bool {
    return PhotoBridge.shared.openSettings()
}

// MARK: - 权限请求的异步桥接

/// 权限请求回调类型
public typealias PermissionCallback = @convention(c) (Int32) -> Void

/// 存储权限请求回调的全局变量
private var permissionCallback: PermissionCallback?

/// 请求权限的 C 兼容函数
@_cdecl("photo_bridge_request_permission")
public func photoBridgeRequestPermission(callback: @escaping PermissionCallback) {
    permissionCallback = callback
    
    PhotoBridge.shared.requestPermission { status in
        callback(status)
        permissionCallback = nil
    }
}

// MARK: - 缩略图数据获取的异步桥接

/// 缩略图数据回调类型
public typealias ThumbnailDataCallback = @convention(c) (UnsafePointer<UInt8>?, Int32) -> Void

/// 获取缩略图数据的 C 兼容函数
@_cdecl("photo_bridge_get_thumbnail_data")
public func photoBridgeGetThumbnailData(
    assetId: UnsafePointer<CChar>,
    width: UInt32,
    height: UInt32,
    callback: @escaping ThumbnailDataCallback
) {
    let assetIdString = String(cString: assetId)
    
    PhotoBridge.shared.getThumbnailData(
        assetId: assetIdString,
        width: width,
        height: height
    ) { data in
        if let data = data {
            data.withUnsafeBytes { bytes in
                let pointer = bytes.bindMemory(to: UInt8.self).baseAddress
                callback(pointer, Int32(data.count))
            }
        } else {
            callback(nil, 0)
        }
    }
}
