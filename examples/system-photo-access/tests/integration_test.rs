//! 集成测试
//!
//! 测试 Rust 和 Swift 之间的互操作功能

use system_photo_access::permissions::{PhotoPermissionStatus, check_permission_status};
use system_photo_access::error::PhotoError;
use std::time::Instant;

/// 测试 Swift 桥接函数的基本调用
#[test]
fn test_swift_bridge_basic_call() {
    // 测试基本的权限检查调用
    let result = check_permission_status();
    
    match result {
        Ok(status) => {
            println!("Swift 桥接调用成功，权限状态: {:?}", status);
            
            // 验证返回的状态是有效的枚举值
            let valid_statuses = [
                PhotoPermissionStatus::NotDetermined,
                PhotoPermissionStatus::Restricted,
                PhotoPermissionStatus::Denied,
                PhotoPermissionStatus::Authorized,
                PhotoPermissionStatus::Limited,
            ];
            
            assert!(valid_statuses.contains(&status), 
                   "Swift 桥接返回的权限状态应该是有效的枚举值");
        }
        Err(error) => {
            panic!("Swift 桥接调用失败: {}", error);
        }
    }
}

/// 测试 Swift 桥接的性能
#[test]
fn test_swift_bridge_performance() {
    const MAX_DURATION_MS: u128 = 50; // Swift 调用应该更快
    const TEST_ITERATIONS: usize = 20;

    let mut durations = Vec::new();

    for i in 0..TEST_ITERATIONS {
        let start = Instant::now();
        let result = check_permission_status();
        let duration = start.elapsed();

        assert!(result.is_ok(), "第 {} 次 Swift 桥接调用失败", i + 1);
        
        durations.push(duration);
        
        // 单次调用不应超过最大允许时间
        assert!(duration.as_millis() <= MAX_DURATION_MS, 
               "第 {} 次 Swift 桥接调用耗时 {}ms，超过了最大允许时间 {}ms", 
               i + 1, duration.as_millis(), MAX_DURATION_MS);
    }

    // 计算统计信息
    let total_duration: std::time::Duration = durations.iter().sum();
    let average_duration = total_duration / TEST_ITERATIONS as u32;
    let min_duration = durations.iter().min().unwrap();
    let max_duration = durations.iter().max().unwrap();

    println!("Swift 桥接性能统计:");
    println!("  平均耗时: {:?}", average_duration);
    println!("  最小耗时: {:?}", min_duration);
    println!("  最大耗时: {:?}", max_duration);
    
    // 平均时间也不应超过最大允许时间
    assert!(average_duration.as_millis() <= MAX_DURATION_MS,
           "Swift 桥接平均耗时 {}ms，超过了最大允许时间 {}ms",
           average_duration.as_millis(), MAX_DURATION_MS);
}

/// 测试 Swift 桥接的稳定性
#[test]
fn test_swift_bridge_stability() {
    const STABILITY_ITERATIONS: usize = 100;
    let mut success_count = 0;
    let mut error_count = 0;

    for i in 0..STABILITY_ITERATIONS {
        let result = check_permission_status();
        
        match result {
            Ok(_) => {
                success_count += 1;
            }
            Err(error) => {
                error_count += 1;
                println!("第 {} 次调用失败: {}", i + 1, error);
            }
        }
    }

    println!("Swift 桥接稳定性统计:");
    println!("  成功次数: {}", success_count);
    println!("  失败次数: {}", error_count);
    println!("  成功率: {:.2}%", (success_count as f64 / STABILITY_ITERATIONS as f64) * 100.0);

    // 成功率应该很高
    assert!(success_count >= STABILITY_ITERATIONS * 95 / 100, 
           "Swift 桥接成功率过低，可能存在稳定性问题");
}

/// 测试权限状态映射的正确性
#[test]
fn test_permission_status_mapping() {
    // 这个测试验证 Swift 端返回的整数值与 Rust 端的枚举映射是否正确
    
    // 多次调用以获取不同的权限状态（如果可能）
    let mut observed_statuses = std::collections::HashSet::new();
    
    for _ in 0..10 {
        if let Ok(status) = check_permission_status() {
            observed_statuses.insert(status);
            
            // 验证状态的整数表示是否在有效范围内
            let status_int = status.to_i32();
            assert!(status_int >= 0 && status_int <= 4, 
                   "权限状态的整数表示 {} 超出有效范围 [0, 4]", status_int);
            
            // 验证往返转换的一致性
            let converted_back = PhotoPermissionStatus::from_i32(status_int);
            assert_eq!(converted_back, status, 
                      "权限状态往返转换不一致: {:?} -> {} -> {:?}", 
                      status, status_int, converted_back);
        }
    }
    
    println!("观察到的权限状态: {:?}", observed_statuses);
    
    // 至少应该观察到一种权限状态
    assert!(!observed_statuses.is_empty(), "应该至少观察到一种权限状态");
}

/// 测试错误处理的集成
#[test]
fn test_error_handling_integration() {
    // 这个测试主要验证错误处理机制在 Rust-Swift 集成中的工作情况
    
    // 正常情况下，权限检查不应该失败
    let result = check_permission_status();
    
    match result {
        Ok(status) => {
            println!("权限检查成功: {:?}", status);
            
            // 验证状态的有效性
            assert!(matches!(status, 
                PhotoPermissionStatus::NotDetermined |
                PhotoPermissionStatus::Restricted |
                PhotoPermissionStatus::Denied |
                PhotoPermissionStatus::Authorized |
                PhotoPermissionStatus::Limited
            ), "返回的权限状态应该是有效的枚举值");
        }
        Err(error) => {
            // 如果确实发生错误，验证错误类型是否合理
            println!("权限检查失败: {}", error);
            
            match error {
                PhotoError::SystemError(_) => {
                    // 系统错误是可能的
                    println!("系统错误，这在某些环境中是正常的");
                }
                _ => {
                    panic!("意外的错误类型: {}", error);
                }
            }
        }
    }
}

/// 测试内存安全性
#[test]
fn test_memory_safety() {
    // 这个测试通过大量调用来检测潜在的内存问题
    const MEMORY_TEST_ITERATIONS: usize = 1000;
    
    for i in 0..MEMORY_TEST_ITERATIONS {
        let result = check_permission_status();
        
        // 验证每次调用都能正常完成
        assert!(result.is_ok() || matches!(result, Err(PhotoError::SystemError(_))), 
               "第 {} 次内存安全测试失败: {:?}", i + 1, result);
        
        // 每100次调用打印一次进度
        if (i + 1) % 100 == 0 {
            println!("内存安全测试进度: {}/{}", i + 1, MEMORY_TEST_ITERATIONS);
        }
    }
    
    println!("内存安全测试完成，共执行 {} 次调用", MEMORY_TEST_ITERATIONS);
}

/// 测试线程安全性
#[test]
fn test_thread_safety() {
    use std::thread;
    use std::sync::Arc;
    use std::sync::atomic::{AtomicUsize, Ordering};
    
    const THREAD_COUNT: usize = 5;
    const CALLS_PER_THREAD: usize = 20;
    
    let success_counter = Arc::new(AtomicUsize::new(0));
    let error_counter = Arc::new(AtomicUsize::new(0));
    
    let mut handles = Vec::new();
    
    for thread_id in 0..THREAD_COUNT {
        let success_counter = Arc::clone(&success_counter);
        let error_counter = Arc::clone(&error_counter);
        
        let handle = thread::spawn(move || {
            for call_id in 0..CALLS_PER_THREAD {
                let result = check_permission_status();
                
                match result {
                    Ok(status) => {
                        success_counter.fetch_add(1, Ordering::SeqCst);
                        println!("线程 {} 调用 {} 成功: {:?}", thread_id, call_id, status);
                    }
                    Err(error) => {
                        error_counter.fetch_add(1, Ordering::SeqCst);
                        println!("线程 {} 调用 {} 失败: {}", thread_id, call_id, error);
                    }
                }
                
                // 短暂延迟以避免过于频繁的调用
                thread::sleep(std::time::Duration::from_millis(10));
            }
        });
        
        handles.push(handle);
    }
    
    // 等待所有线程完成
    for handle in handles {
        handle.join().expect("线程应该成功完成");
    }
    
    let total_success = success_counter.load(Ordering::SeqCst);
    let total_errors = error_counter.load(Ordering::SeqCst);
    let total_calls = THREAD_COUNT * CALLS_PER_THREAD;
    
    println!("线程安全测试结果:");
    println!("  总调用次数: {}", total_calls);
    println!("  成功次数: {}", total_success);
    println!("  失败次数: {}", total_errors);
    println!("  成功率: {:.2}%", (total_success as f64 / total_calls as f64) * 100.0);
    
    // 验证所有调用都有结果
    assert_eq!(total_success + total_errors, total_calls, 
              "成功和失败的调用次数之和应该等于总调用次数");
    
    // 成功率应该很高
    assert!(total_success >= total_calls * 90 / 100, 
           "多线程环境下的成功率过低，可能存在线程安全问题");
}
